import 'local_ocr_service.dart';
import 'free_ocr_service.dart';

enum ConversionMethod {
  localOcr, // Google ML Kit محلي
  freeOcr, // OCR.space مجاني
  hybrid, // مزيج من الطرق
}

class MathpixService {
  /// تحويل الصورة إلى كود LaTeX باستخدام طرق بديلة مجانية
  static Future<String> convertImageToLatex(
    String imagePath, {
    ConversionMethod method = ConversionMethod.hybrid,
  }) async {
    try {
      String extractedText = '';
      String latexCode = '';

      switch (method) {
        case ConversionMethod.localOcr:
          // استخدام Google ML Kit المحلي
          extractedText = await LocalOcrService.extractTextFromImage(imagePath);
          latexCode = LocalOcrService.convertTextToLatex(extractedText);
          break;

        case ConversionMethod.freeOcr:
          // استخدام OCR.space المجاني
          extractedText = await FreeOcrService.extractTextFromImage(imagePath);
          latexCode = FreeOcrService.convertTextToLatex(extractedText);
          break;

        case ConversionMethod.hybrid:
          // جرب الطريقة المحلية أولاً، ثم المجانية
          try {
            extractedText = await LocalOcrService.extractTextFromImage(
              imagePath,
            );
            latexCode = LocalOcrService.convertTextToLatex(extractedText);

            // إذا كانت النتيجة ضعيفة، جرب الطريقة المجانية
            if (latexCode.length < 10 || !_isValidLatex(latexCode)) {
              extractedText = await FreeOcrService.extractTextFromImage(
                imagePath,
              );
              latexCode = FreeOcrService.convertTextToLatex(extractedText);
            }
          } catch (e) {
            // إذا فشلت الطريقة المحلية، استخدم المجانية
            extractedText = await FreeOcrService.extractTextFromImage(
              imagePath,
            );
            latexCode = FreeOcrService.convertTextToLatex(extractedText);
          }
          break;
      }

      if (latexCode.isEmpty) {
        throw Exception('لم يتم العثور على معادلات في الصورة');
      }

      return latexCode;
    } catch (e) {
      throw Exception('خطأ في تحويل الصورة: ${e.toString()}');
    }
  }

  /// التحقق من صحة كود LaTeX
  static bool _isValidLatex(String latex) {
    // فحص بسيط لصحة LaTeX
    if (latex.isEmpty) return false;

    // يجب أن يحتوي على رموز رياضية أو LaTeX
    List<String> mathIndicators = [
      r'\frac',
      r'\sqrt',
      r'\sum',
      r'\int',
      r'\alpha',
      r'\beta',
      r'\gamma',
      r'\delta',
      r'\pi',
      r'\theta',
      r'\lambda',
      '^{',
      '_{',
      r'\times',
      r'\div',
      r'\pm',
      r'\neq',
    ];

    return mathIndicators.any((indicator) => latex.contains(indicator));
  }

  /// التحقق من توفر الخدمات
  static bool areKeysValid() {
    // الآن نستخدم خدمات مجانية، لذا دائماً متاح
    return true;
  }

  /// تنظيف الموارد
  static void dispose() {
    LocalOcrService.dispose();
  }
}

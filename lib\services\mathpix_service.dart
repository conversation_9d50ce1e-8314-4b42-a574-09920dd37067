import 'local_ocr_service.dart';
import 'free_ocr_service.dart';

enum ConversionMethod {
  localOcr, // Google ML Kit محلي
  freeOcr, // OCR.space مجاني
  hybrid, // مزيج من الطرق
}

class MathpixService {
  /// تحويل الصورة إلى كود LaTeX باستخدام طرق بديلة مجانية
  static Future<String> convertImageToLatex(
    String imagePath, {
    ConversionMethod method = ConversionMethod.hybrid,
  }) async {
    try {
      String extractedText = '';
      String latexCode = '';

      switch (method) {
        case ConversionMethod.localOcr:
          // استخدام Google ML Kit المحلي
          extractedText = await LocalOcrService.extractTextFromImage(imagePath);
          latexCode = LocalOcrService.convertTextToLatex(extractedText);
          break;

        case ConversionMethod.freeOcr:
          // استخدام OCR.space المجاني
          extractedText = await FreeOcrService.extractTextFromImage(imagePath);
          latexCode = FreeOcrService.convertTextToLatex(extractedText);
          break;

        case ConversionMethod.hybrid:
          // جرب الطريقة المحلية أولاً (أسرع وأكثر موثوقية)
          try {
            extractedText = await LocalOcrService.extractTextFromImage(
              imagePath,
            );

            if (extractedText.trim().isNotEmpty) {
              latexCode = LocalOcrService.convertTextToLatex(extractedText);

              // إذا كانت النتيجة جيدة، استخدمها
              if (latexCode.isNotEmpty && latexCode.length > 3) {
                break;
              }
            }
          } catch (e) {
            // تجاهل الخطأ وجرب الطريقة التالية
          }

          // إذا فشلت الطريقة المحلية أو كانت النتيجة ضعيفة، جرب الخدمة المجانية
          try {
            extractedText = await FreeOcrService.extractTextFromImage(
              imagePath,
            );
            latexCode = FreeOcrService.convertTextToLatex(extractedText);
          } catch (e) {
            // إذا فشلت كلا الطريقتين، ارمي الخطأ
            throw Exception(
              'فشل في استخراج النص بكلا الطريقتين. تحقق من الصورة واتصال الإنترنت',
            );
          }
          break;
      }

      if (latexCode.isEmpty) {
        throw Exception('لم يتم العثور على معادلات في الصورة');
      }

      return latexCode;
    } catch (e) {
      throw Exception('خطأ في تحويل الصورة: ${e.toString()}');
    }
  }

  /// التحقق من توفر الخدمات
  static bool areKeysValid() {
    // الآن نستخدم خدمات مجانية، لذا دائماً متاح
    return true;
  }

  /// تنظيف الموارد
  static void dispose() {
    LocalOcrService.dispose();
  }
}

import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import '../config/api_config.dart';

class MathpixService {
  /// تحويل الصورة إلى كود LaTeX باستخدام Mathpix API
  static Future<String> convertImageToLatex(String imagePath) async {
    try {
      // قراءة الصورة وتحويلها إلى base64
      final bytes = await File(imagePath).readAsBytes();
      final base64Image = base64Encode(bytes);

      // إعداد البيانات للإرسال
      final requestBody = {
        'src': 'data:image/jpeg;base64,$base64Image',
        'formats': ['latex_styled'],
        'data_options': {'include_asciimath': true, 'include_latex': true},
      };

      // إرسال الطلب إلى Mathpix API
      final response = await http.post(
        Uri.parse(ApiConfig.mathpixBaseUrl),
        headers: {
          'app_id': ApiConfig.mathpixAppId,
          'app_key': ApiConfig.mathpixAppKey,
          'Content-Type': 'application/json',
        },
        body: jsonEncode(requestBody),
      );

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);

        // التحقق من وجود LaTeX في الاستجابة
        if (responseData['latex_styled'] != null) {
          return responseData['latex_styled'];
        } else if (responseData['text'] != null) {
          return responseData['text'];
        } else {
          throw Exception('لم يتم العثور على نص في الصورة');
        }
      } else {
        final errorData = jsonDecode(response.body);
        throw Exception('خطأ في API: ${errorData['error'] ?? 'خطأ غير معروف'}');
      }
    } catch (e) {
      if (e.toString().contains('SocketException')) {
        throw Exception('تحقق من اتصال الإنترنت');
      } else if (!ApiConfig.areKeysConfigured) {
        throw Exception(ApiConfig.keysNotConfiguredMessage);
      } else {
        throw Exception('خطأ في تحويل الصورة: ${e.toString()}');
      }
    }
  }

  /// التحقق من صحة مفاتيح API
  static bool areKeysValid() {
    return ApiConfig.areKeysConfigured;
  }
}

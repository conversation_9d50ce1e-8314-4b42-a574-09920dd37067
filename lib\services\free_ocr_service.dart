import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import '../config/api_config.dart';

class FreeOcrService {
  /// تحويل الصورة إلى نص باستخدام OCR.space API المجاني
  static Future<String> extractTextFromImage(String imagePath) async {
    try {
      final bytes = await File(imagePath).readAsBytes();
      final base64Image = base64Encode(bytes);

      final response = await http.post(
        Uri.parse(ApiConfig.ocrSpaceBaseUrl),
        headers: {'Content-Type': 'application/x-www-form-urlencoded'},
        body: {
          'apikey': ApiConfig.ocrSpaceApiKey,
          'base64Image': 'data:image/jpeg;base64,$base64Image',
          'language': 'eng',
          'isOverlayRequired': 'false',
          'detectOrientation': 'true',
          'scale': 'true',
          'OCREngine': '2', // استخدام المحرك الأحدث
        },
      );

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);

        if (responseData['IsErroredOnProcessing'] == false) {
          final parsedResults = responseData['ParsedResults'] as List;
          if (parsedResults.isNotEmpty) {
            return parsedResults[0]['ParsedText'] ?? '';
          }
        } else {
          throw Exception(
            'خطأ في معالجة الصورة: ${responseData['ErrorMessage']}',
          );
        }
      } else {
        throw Exception('خطأ في الاتصال بخدمة OCR');
      }

      return '';
    } catch (e) {
      throw Exception('خطأ في استخراج النص: ${e.toString()}');
    }
  }

  /// تحويل النص إلى LaTeX باستخدام خوارزمية بسيطة ومحسنة
  static String convertTextToLatex(String extractedText) {
    if (extractedText.isEmpty) {
      throw Exception('لم يتم العثور على نص في الصورة');
    }

    String cleanText = extractedText.trim();

    // تطبيق تحويل بسيط وفعال
    String latexCode = _simpleLatexConversion(cleanText);

    return latexCode;
  }

  /// تحويل بسيط وفعال إلى LaTeX
  static String _simpleLatexConversion(String text) {
    String result = text;

    // تنظيف النص
    result = result.replaceAll(RegExp(r'\s+'), ' ').trim();

    // تحويل الرموز الأساسية
    result = _convertBasicSymbols(result);

    // تحويل الكسور البسيطة
    result = _convertSimpleFractions(result);

    // تحويل الأسس البسيطة
    result = _convertSimplePowers(result);

    // تنظيف نهائي
    result = _cleanupResult(result);

    // إضافة علامات LaTeX
    if (!result.startsWith(r'$')) {
      result = r'$' + result + r'$';
    }

    return result;
  }

  /// تحويل الرموز الأساسية
  static String _convertBasicSymbols(String text) {
    Map<String, String> symbolMap = {
      '±': r'\pm',
      '×': r'\times',
      '÷': r'\div',
      '≠': r'\neq',
      '≤': r'\leq',
      '≥': r'\geq',
      '∞': r'\infty',
      'π': r'\pi',
      '√': r'\sqrt',
      '∑': r'\sum',
      '∫': r'\int',
    };

    String result = text;
    symbolMap.forEach((symbol, latex) {
      result = result.replaceAll(symbol, latex);
    });

    return result;
  }

  /// تحويل الكسور البسيطة
  static String _convertSimpleFractions(String text) {
    RegExp fractionPattern = RegExp(r'(\w+)/(\w+)');
    return text.replaceAllMapped(fractionPattern, (match) {
      String numerator = match.group(1)!;
      String denominator = match.group(2)!;
      return '\\frac{$numerator}{$denominator}';
    });
  }

  /// تحويل الأسس البسيطة
  static String _convertSimplePowers(String text) {
    RegExp powerPattern = RegExp(r'(\w+)\^(\w+|\d+)');
    return text.replaceAllMapped(powerPattern, (match) {
      String base = match.group(1)!;
      String exponent = match.group(2)!;
      return '$base^{$exponent}';
    });
  }

  /// تنظيف النتيجة
  static String _cleanupResult(String text) {
    String result = text;
    result = result.replaceAll(RegExp(r'\s+'), ' ');
    result = result.trim();
    result = result.replaceAll('( ', '(');
    result = result.replaceAll(' )', ')');
    return result;
  }
}

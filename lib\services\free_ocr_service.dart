import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import '../config/api_config.dart';

class FreeOcrService {
  /// تحويل الصورة إلى نص باستخدام OCR.space API المجاني
  static Future<String> extractTextFromImage(String imagePath) async {
    try {
      final bytes = await File(imagePath).readAsBytes();
      final base64Image = base64Encode(bytes);

      final response = await http.post(
        Uri.parse(ApiConfig.ocrSpaceBaseUrl),
        headers: {'Content-Type': 'application/x-www-form-urlencoded'},
        body: {
          'apikey': ApiConfig.ocrSpaceApiKey,
          'base64Image': 'data:image/jpeg;base64,$base64Image',
          'language': 'eng',
          'isOverlayRequired': 'false',
          'detectOrientation': 'true',
          'scale': 'true',
          'OCREngine': '2', // استخدام المحرك الأحدث
        },
      );

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);

        if (responseData['IsErroredOnProcessing'] == false) {
          final parsedResults = responseData['ParsedResults'] as List;
          if (parsedResults.isNotEmpty) {
            return parsedResults[0]['ParsedText'] ?? '';
          }
        } else {
          throw Exception(
            'خطأ في معالجة الصورة: ${responseData['ErrorMessage']}',
          );
        }
      } else {
        throw Exception('خطأ في الاتصال بخدمة OCR');
      }

      return '';
    } catch (e) {
      throw Exception('خطأ في استخراج النص: ${e.toString()}');
    }
  }

  /// تحويل النص إلى LaTeX باستخدام خوارزمية محسنة
  static String convertTextToLatex(String extractedText) {
    if (extractedText.isEmpty) {
      throw Exception('لم يتم العثور على نص في الصورة');
    }

    String cleanText = extractedText.trim();

    // تطبيق تحويلات LaTeX المتقدمة
    String latexCode = _advancedMathConversion(cleanText);

    return latexCode;
  }

  /// تحويل رياضي متقدم
  static String _advancedMathConversion(String text) {
    String result = text;

    // إزالة المسافات الزائدة
    result = result.replaceAll(RegExp(r'\s+'), ' ').trim();

    // تحويل الأرقام والمتغيرات
    result = _convertVariablesAndNumbers(result);

    // تحويل العمليات الرياضية
    result = _convertMathOperations(result);

    // تحويل الكسور المعقدة
    result = _convertComplexFractions(result);

    // تحويل الجذور والأسس
    result = _convertRootsAndPowers(result);

    // تحويل المصفوفات والمحددات
    result = _convertMatrices(result);

    // تحويل التكاملات والمشتقات
    result = _convertCalculus(result);

    // تحويل المعادلات الكيميائية
    result = _convertChemicalEquations(result);

    // تنسيق نهائي
    result = _finalFormatting(result);

    return result;
  }

  static String _convertVariablesAndNumbers(String text) {
    String result = text;

    // تحويل الأرقام المرفوعة (superscript)
    Map<String, String> superscriptMap = {
      '⁰': '^{0}',
      '¹': '^{1}',
      '²': '^{2}',
      '³': '^{3}',
      '⁴': '^{4}',
      '⁵': '^{5}',
      '⁶': '^{6}',
      '⁷': '^{7}',
      '⁸': '^{8}',
      '⁹': '^{9}',
    };

    // تحويل الأرقام المنخفضة (subscript)
    Map<String, String> subscriptMap = {
      '₀': '_{0}',
      '₁': '_{1}',
      '₂': '_{2}',
      '₃': '_{3}',
      '₄': '_{4}',
      '₅': '_{5}',
      '₆': '_{6}',
      '₇': '_{7}',
      '₈': '_{8}',
      '₉': '_{9}',
    };

    superscriptMap.forEach((key, value) {
      result = result.replaceAll(key, value);
    });

    subscriptMap.forEach((key, value) {
      result = result.replaceAll(key, value);
    });

    return result;
  }

  static String _convertMathOperations(String text) {
    Map<String, String> operationMap = {
      '±': r'\pm',
      '∓': r'\mp',
      '×': r'\times',
      '·': r'\cdot',
      '÷': r'\div',
      '≠': r'\neq',
      '≈': r'\approx',
      '≡': r'\equiv',
      '≤': r'\leq',
      '≥': r'\geq',
      '<<': r'\ll',
      '>>': r'\gg',
      '∝': r'\propto',
      '∞': r'\infty',
    };

    String result = text;
    operationMap.forEach((symbol, latex) {
      result = result.replaceAll(symbol, latex);
    });

    return result;
  }

  static String _convertComplexFractions(String text) {
    String result = text;

    // كسور معقدة مثل (a+b)/(c+d)
    RegExp complexFractionPattern = RegExp(r'\(([^)]+)\)/\(([^)]+)\)');
    result = result.replaceAllMapped(complexFractionPattern, (match) {
      return r'\frac{' + match.group(1)! + '}{' + match.group(2)! + '}';
    });

    // كسور بسيطة
    RegExp simpleFractionPattern = RegExp(r'(\w+|\d+)/(\w+|\d+)');
    result = result.replaceAllMapped(simpleFractionPattern, (match) {
      return r'\frac{' + match.group(1)! + '}{' + match.group(2)! + '}';
    });

    return result;
  }

  static String _convertRootsAndPowers(String text) {
    String result = text;

    // جذور مربعة
    result = result.replaceAll('√', r'\sqrt');
    result = result.replaceAll(RegExp(r'sqrt\(([^)]+)\)'), r'\sqrt{$1}');

    // جذور نونية
    RegExp nthRootPattern = RegExp(r'(\d+)√\(([^)]+)\)');
    result = result.replaceAllMapped(nthRootPattern, (match) {
      return r'\sqrt[' + match.group(1)! + ']{' + match.group(2)! + '}';
    });

    // أسس
    RegExp powerPattern = RegExp(r'(\w+)\^(\w+|\d+)');
    result = result.replaceAllMapped(powerPattern, (match) {
      return match.group(1)! + '^{' + match.group(2)! + '}';
    });

    return result;
  }

  static String _convertMatrices(String text) {
    String result = text;

    // تحويل المصفوفات البسيطة
    if (result.contains('|') && result.split('|').length > 2) {
      // محدد مصفوفة
      result = result.replaceAll('|', r'\begin{vmatrix}') + r'\end{vmatrix}';
    }

    return result;
  }

  static String _convertCalculus(String text) {
    String result = text;

    // تكاملات
    result = result.replaceAll('∫', r'\int');
    result = result.replaceAll('∬', r'\iint');
    result = result.replaceAll('∭', r'\iiint');

    // مشتقات
    result = result.replaceAll('∂', r'\partial');
    result = result.replaceAll('d/dx', r'\frac{d}{dx}');

    // حدود
    result = result.replaceAll('lim', r'\lim');
    result = result.replaceAll('→', r'\to');

    return result;
  }

  static String _convertChemicalEquations(String text) {
    String result = text;

    // عناصر كيميائية شائعة
    Map<String, String> chemicalElements = {
      'H2O': r'H_2O',
      'CO2': r'CO_2',
      'H2SO4': r'H_2SO_4',
      'NaCl': r'NaCl',
      'CaCO3': r'CaCO_3',
    };

    chemicalElements.forEach((formula, latex) {
      result = result.replaceAll(formula, latex);
    });

    // تحويل الأرقام في الصيغ الكيميائية
    RegExp chemicalPattern = RegExp(r'([A-Z][a-z]?)(\d+)');
    result = result.replaceAllMapped(chemicalPattern, (match) {
      return match.group(1)! + '_{' + match.group(2)! + '}';
    });

    return result;
  }

  static String _finalFormatting(String text) {
    String result = text;

    // إضافة تنسيق LaTeX إذا لم يكن موجوداً
    if (!result.startsWith(r'$') &&
        !result.startsWith(r'\[') &&
        !result.startsWith(r'\begin{')) {
      result = r'$' + result + r'$';
    }

    // تنظيف المسافات
    result = result.replaceAll(RegExp(r'\s+'), ' ').trim();

    return result;
  }
}

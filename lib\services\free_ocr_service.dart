import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import '../config/api_config.dart';

class FreeOcrService {
  /// تحويل الصورة إلى نص باستخدام OCR.space API المجاني
  static Future<String> extractTextFromImage(String imagePath) async {
    http.Client? client;
    try {
      final bytes = await File(imagePath).readAsBytes();
      final base64Image = base64Encode(bytes);

      // إنشاء client مع إعدادات محسنة
      client = http.Client();

      final response = await client
          .post(
            Uri.parse(ApiConfig.ocrSpaceBaseUrl),
            headers: {
              'Content-Type': 'application/x-www-form-urlencoded',
              'User-Agent': 'Flutter-LaTeX-App/1.0',
              'Accept': 'application/json',
            },
            body: {
              'apikey': ApiConfig.ocrSpaceApiKey,
              'base64Image': 'data:image/jpeg;base64,$base64Image',
              'language': 'eng',
              'isOverlayRequired': 'false',
              'detectOrientation': 'true',
              'scale': 'true',
              'OCREngine': '2', // استخدام المحرك الأحدث
              'filetype': 'JPG',
            },
          )
          .timeout(
            Duration(seconds: 30), // مهلة زمنية 30 ثانية
            onTimeout: () {
              throw Exception(
                'انتهت مهلة الاتصال بالخدمة. يرجى المحاولة مرة أخرى',
              );
            },
          );

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);

        // التحقق من وجود أخطاء في الاستجابة
        if (responseData['IsErroredOnProcessing'] == true) {
          String errorMessage = 'خطأ غير معروف';
          if (responseData['ErrorMessage'] != null) {
            if (responseData['ErrorMessage'] is List) {
              errorMessage = (responseData['ErrorMessage'] as List).join(', ');
            } else {
              errorMessage = responseData['ErrorMessage'].toString();
            }
          }
          throw Exception('خطأ من الخدمة: $errorMessage');
        }

        if (responseData['ParsedResults'] != null) {
          final parsedResults = responseData['ParsedResults'] as List;
          if (parsedResults.isNotEmpty) {
            String extractedText = parsedResults[0]['ParsedText'] ?? '';
            if (extractedText.trim().isEmpty) {
              throw Exception('لم يتم العثور على نص في الصورة');
            }
            return extractedText;
          }
        }

        throw Exception('لم يتم العثور على نتائج في الاستجابة');
      } else {
        throw Exception(
          'خطأ في الخادم: ${response.statusCode} - ${response.reasonPhrase}',
        );
      }
    } on SocketException {
      throw Exception('خطأ في الاتصال بالإنترنت. تحقق من اتصالك بالشبكة');
    } on HttpException {
      throw Exception('خطأ في الاتصال بالخادم');
    } on FormatException {
      throw Exception('خطأ في تحليل استجابة الخادم');
    } catch (e) {
      if (e.toString().contains('Connection reset by peer')) {
        throw Exception('انقطع الاتصال بالخادم. يرجى المحاولة مرة أخرى');
      } else if (e.toString().contains('timeout')) {
        throw Exception('انتهت مهلة الاتصال. يرجى المحاولة مرة أخرى');
      }
      throw Exception('خطأ في استخراج النص: ${e.toString()}');
    } finally {
      // إغلاق الاتصال
      client?.close();
    }
  }

  /// تحويل النص إلى LaTeX باستخدام خوارزمية بسيطة ومحسنة
  static String convertTextToLatex(String extractedText) {
    if (extractedText.isEmpty) {
      throw Exception('لم يتم العثور على نص في الصورة');
    }

    String cleanText = extractedText.trim();

    // تطبيق تحويل بسيط وفعال
    String latexCode = _simpleLatexConversion(cleanText);

    return latexCode;
  }

  /// تحويل بسيط وفعال إلى LaTeX
  static String _simpleLatexConversion(String text) {
    String result = text;

    // تنظيف النص
    result = result.replaceAll(RegExp(r'\s+'), ' ').trim();

    // تحويل الرموز الأساسية
    result = _convertBasicSymbols(result);

    // تحويل الكسور البسيطة
    result = _convertSimpleFractions(result);

    // تحويل الأسس البسيطة
    result = _convertSimplePowers(result);

    // تنظيف نهائي
    result = _cleanupResult(result);

    // إضافة علامات LaTeX
    if (!result.startsWith(r'$')) {
      result = r'$' + result + r'$';
    }

    return result;
  }

  /// تحويل الرموز الأساسية
  static String _convertBasicSymbols(String text) {
    Map<String, String> symbolMap = {
      '±': r'\pm',
      '×': r'\times',
      '÷': r'\div',
      '≠': r'\neq',
      '≤': r'\leq',
      '≥': r'\geq',
      '∞': r'\infty',
      'π': r'\pi',
      '√': r'\sqrt',
      '∑': r'\sum',
      '∫': r'\int',
    };

    String result = text;
    symbolMap.forEach((symbol, latex) {
      result = result.replaceAll(symbol, latex);
    });

    return result;
  }

  /// تحويل الكسور البسيطة
  static String _convertSimpleFractions(String text) {
    RegExp fractionPattern = RegExp(r'(\w+)/(\w+)');
    return text.replaceAllMapped(fractionPattern, (match) {
      String numerator = match.group(1)!;
      String denominator = match.group(2)!;
      return '\\frac{$numerator}{$denominator}';
    });
  }

  /// تحويل الأسس البسيطة
  static String _convertSimplePowers(String text) {
    RegExp powerPattern = RegExp(r'(\w+)\^(\w+|\d+)');
    return text.replaceAllMapped(powerPattern, (match) {
      String base = match.group(1)!;
      String exponent = match.group(2)!;
      return '$base^{$exponent}';
    });
  }

  /// تنظيف النتيجة
  static String _cleanupResult(String text) {
    String result = text;
    result = result.replaceAll(RegExp(r'\s+'), ' ');
    result = result.trim();
    result = result.replaceAll('( ', '(');
    result = result.replaceAll(' )', ')');
    return result;
  }
}

import 'package:google_mlkit_text_recognition/google_mlkit_text_recognition.dart';

class LocalOcrService {
  static final TextRecognizer _textRecognizer = TextRecognizer();

  /// استخراج النص من الصورة باستخدام Google ML Kit
  static Future<String> extractTextFromImage(String imagePath) async {
    try {
      final inputImage = InputImage.fromFilePath(imagePath);
      final RecognizedText recognizedText = await _textRecognizer.processImage(
        inputImage,
      );

      return recognizedText.text;
    } catch (e) {
      throw Exception('خطأ في استخراج النص من الصورة: ${e.toString()}');
    }
  }

  /// تحويل النص المستخرج إلى LaTeX
  static String convertTextToLatex(String extractedText) {
    if (extractedText.isEmpty) {
      throw Exception('لم يتم العثور على نص في الصورة');
    }

    // تنظيف النص
    String cleanText = extractedText.trim();

    // إزالة الأحرف غير المرغوب فيها
    cleanText = _cleanText(cleanText);

    // تحويل ذكي للنص إلى LaTeX
    String latexCode = _smartTextToLatex(cleanText);

    return latexCode;
  }

  /// تحويل ذكي للنص إلى LaTeX
  static String _smartTextToLatex(String text) {
    String result = text;

    // تحويل الرموز الرياضية الأساسية
    result = _convertMathSymbols(result);

    // تحويل الكسور البسيطة
    result = _convertSimpleFractions(result);

    // تحويل الأسس البسيطة
    result = _convertSimplePowers(result);

    // تنظيف النتيجة النهائية
    result = _finalCleanup(result);

    // إضافة علامات LaTeX
    if (!result.startsWith(r'$')) {
      result = r'$' + result + r'$';
    }

    return result;
  }

  /// تنظيف النص من الأحرف غير المرغوب فيها
  static String _cleanText(String text) {
    String result = text;

    // إزالة الأحرف الخاصة التي قد تسبب مشاكل في LaTeX
    result = result.replaceAll(
      RegExp(
        r'[""'
        '`]',
      ),
      '',
    ); // إزالة علامات الاقتباس المختلفة
    result = result.replaceAll(RegExp(r'\s+'), ' '); // توحيد المسافات
    result = result.replaceAll(
      RegExp(r'^\s+|\s+$'),
      '',
    ); // إزالة المسافات من البداية والنهاية

    return result;
  }

  /// تحويل الرموز الرياضية الأساسية
  static String _convertMathSymbols(String text) {
    Map<String, String> symbolMap = {
      '±': r'\pm',
      '∓': r'\mp',
      '×': r'\times',
      '÷': r'\div',
      '≠': r'\neq',
      '≤': r'\leq',
      '≥': r'\geq',
      '∞': r'\infty',
      'π': r'\pi',
      'α': r'\alpha',
      'β': r'\beta',
      'γ': r'\gamma',
      'δ': r'\delta',
      'ε': r'\epsilon',
      'θ': r'\theta',
      'λ': r'\lambda',
      'μ': r'\mu',
      'σ': r'\sigma',
      'φ': r'\phi',
      'ω': r'\omega',
      'Δ': r'\Delta',
      'Σ': r'\Sigma',
      'Π': r'\Pi',
      'Ω': r'\Omega',
      '∑': r'\sum',
      '∏': r'\prod',
      '∫': r'\int',
      '∂': r'\partial',
      '∇': r'\nabla',
      '√': r'\sqrt',
      '∈': r'\in',
      '∉': r'\notin',
      '⊂': r'\subset',
      '⊃': r'\supset',
      '∪': r'\cup',
      '∩': r'\cap',
      '→': r'\rightarrow',
      '←': r'\leftarrow',
      '↔': r'\leftrightarrow',
      '⇒': r'\Rightarrow',
      '⇐': r'\Leftarrow',
      '⇔': r'\Leftrightarrow',
    };

    String result = text;
    symbolMap.forEach((symbol, latex) {
      result = result.replaceAll(symbol, latex);
    });

    return result;
  }

  /// تحويل الكسور البسيطة
  static String _convertSimpleFractions(String text) {
    // البحث عن أنماط الكسور البسيطة مثل "1/2" أو "n/2"
    RegExp fractionPattern = RegExp(r'(\w+)/(\w+)');
    return text.replaceAllMapped(fractionPattern, (match) {
      String numerator = match.group(1)!;
      String denominator = match.group(2)!;
      return '\\frac{$numerator}{$denominator}';
    });
  }

  /// تحويل الأسس البسيطة
  static String _convertSimplePowers(String text) {
    String result = text;

    // تحويل الأسس مثل "x^2" أو "n^2"
    RegExp powerPattern = RegExp(r'(\w+)\^(\w+|\d+)');
    result = result.replaceAllMapped(powerPattern, (match) {
      String base = match.group(1)!;
      String exponent = match.group(2)!;
      return '$base^{$exponent}';
    });

    // تحويل الجذور
    result = result.replaceAll(RegExp(r'sqrt\(([^)]+)\)'), r'\sqrt{$1}');
    result = result.replaceAll(RegExp(r'√\(([^)]+)\)'), r'\sqrt{$1}');

    return result;
  }

  /// تنظيف النتيجة النهائية
  static String _finalCleanup(String text) {
    String result = text;

    // إزالة المسافات الزائدة
    result = result.replaceAll(RegExp(r'\s+'), ' ');
    result = result.trim();

    // إصلاح بعض المشاكل الشائعة
    result = result.replaceAll('( ', '(');
    result = result.replaceAll(' )', ')');
    result = result.replaceAll('{ ', '{');
    result = result.replaceAll(' }', '}');

    return result;
  }

  /// تنظيف الموارد
  static void dispose() {
    _textRecognizer.close();
  }
}

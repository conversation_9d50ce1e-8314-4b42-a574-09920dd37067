import 'package:google_ml_kit/google_ml_kit.dart';

class LocalOcrService {
  static final TextRecognizer _textRecognizer = TextRecognizer();

  /// استخراج النص من الصورة باستخدام Google ML Kit
  static Future<String> extractTextFromImage(String imagePath) async {
    try {
      final inputImage = InputImage.fromFilePath(imagePath);
      final RecognizedText recognizedText = await _textRecognizer.processImage(
        inputImage,
      );

      return recognizedText.text;
    } catch (e) {
      throw Exception('خطأ في استخراج النص من الصورة: ${e.toString()}');
    }
  }

  /// تحويل النص المستخرج إلى LaTeX
  static String convertTextToLatex(String extractedText) {
    if (extractedText.isEmpty) {
      throw Exception('لم يتم العثور على نص في الصورة');
    }

    // تنظيف النص
    String cleanText = extractedText.trim();

    // تحويل الرموز الرياضية الشائعة إلى LaTeX
    String latexCode = _convertMathSymbols(cleanText);

    // تحويل الكسور
    latexCode = _convertFractions(latexCode);

    // تحويل الأسس والجذور
    latexCode = _convertPowersAndRoots(latexCode);

    // تحويل المعادلات
    latexCode = _convertEquations(latexCode);

    // تحويل الأقواس والرموز الخاصة
    latexCode = _convertBracketsAndSymbols(latexCode);

    // إضافة تنسيق LaTeX الأساسي
    if (!latexCode.startsWith(r'$') && !latexCode.startsWith(r'\[')) {
      latexCode = r'$' + latexCode + r'$';
    }

    return latexCode;
  }

  /// تحويل الرموز الرياضية الأساسية
  static String _convertMathSymbols(String text) {
    Map<String, String> symbolMap = {
      '±': r'\pm',
      '∓': r'\mp',
      '×': r'\times',
      '÷': r'\div',
      '≠': r'\neq',
      '≤': r'\leq',
      '≥': r'\geq',
      '∞': r'\infty',
      'π': r'\pi',
      'α': r'\alpha',
      'β': r'\beta',
      'γ': r'\gamma',
      'δ': r'\delta',
      'ε': r'\epsilon',
      'θ': r'\theta',
      'λ': r'\lambda',
      'μ': r'\mu',
      'σ': r'\sigma',
      'φ': r'\phi',
      'ω': r'\omega',
      'Δ': r'\Delta',
      'Σ': r'\Sigma',
      'Π': r'\Pi',
      'Ω': r'\Omega',
      '∑': r'\sum',
      '∏': r'\prod',
      '∫': r'\int',
      '∂': r'\partial',
      '∇': r'\nabla',
      '√': r'\sqrt',
      '∈': r'\in',
      '∉': r'\notin',
      '⊂': r'\subset',
      '⊃': r'\supset',
      '∪': r'\cup',
      '∩': r'\cap',
      '→': r'\rightarrow',
      '←': r'\leftarrow',
      '↔': r'\leftrightarrow',
      '⇒': r'\Rightarrow',
      '⇐': r'\Leftarrow',
      '⇔': r'\Leftrightarrow',
    };

    String result = text;
    symbolMap.forEach((symbol, latex) {
      result = result.replaceAll(symbol, latex);
    });

    return result;
  }

  /// تحويل الكسور
  static String _convertFractions(String text) {
    // البحث عن أنماط الكسور مثل "a/b" أو "1/2"
    RegExp fractionPattern = RegExp(r'(\d+|\w+)/(\d+|\w+)');
    return text.replaceAllMapped(fractionPattern, (match) {
      return r'\frac{' + match.group(1)! + '}{' + match.group(2)! + '}';
    });
  }

  /// تحويل الأسس والجذور
  static String _convertPowersAndRoots(String text) {
    String result = text;

    // تحويل الأسس مثل "x^2" أو "a^n"
    RegExp powerPattern = RegExp(r'(\w+)\^(\w+|\d+)');
    result = result.replaceAllMapped(powerPattern, (match) {
      return match.group(1)! + '^{' + match.group(2)! + '}';
    });

    // تحويل الجذور
    result = result.replaceAll(RegExp(r'sqrt\(([^)]+)\)'), r'\sqrt{$1}');
    result = result.replaceAll(RegExp(r'√\(([^)]+)\)'), r'\sqrt{$1}');

    return result;
  }

  /// تحويل المعادلات
  static String _convertEquations(String text) {
    String result = text;

    // تحويل علامة التساوي في المعادلات
    if (result.contains('=')) {
      // إذا كانت معادلة، نضعها في بيئة equation
      if (!result.startsWith(r'\begin{equation}')) {
        result = r'\begin{equation}' + result + r'\end{equation}';
      }
    }

    return result;
  }

  /// تحويل الأقواس والرموز الخاصة
  static String _convertBracketsAndSymbols(String text) {
    String result = text;

    // تحويل الأقواس الكبيرة
    result = result.replaceAll('(', r'\left(');
    result = result.replaceAll(')', r'\right)');
    result = result.replaceAll('[', r'\left[');
    result = result.replaceAll(']', r'\right]');
    result = result.replaceAll('{', r'\left\{');
    result = result.replaceAll('}', r'\right\}');

    return result;
  }

  /// تنظيف الموارد
  static void dispose() {
    _textRecognizer.close();
  }
}

# دليل البدء السريع 🚀

## خطوات تشغيل التطبيق

### 1. لا حاجة لإعداد مفاتيح API! 🎉

التطبيق الآن يستخدم خدمات OCR مجانية ومدمجة:
- **Google ML Kit** للتعرف على النص محلياً
- **OCR.space** كخدمة مجانية عبر الإنترنت
- **محرك تحويل ذكي** لتحويل النص إلى LaTeX

### 2. تثبيت التبعيات

```bash
flutter pub get
```

### 3. تشغيل التطبيق

```bash
flutter run
```

### 4. اختبار التطبيق

```bash
flutter test
```

## 🎯 طرق التحويل المتاحة

التطبيق يدعم ثلاث طرق للتحويل:

1. **محلي (Local OCR):** استخدام Google ML Kit
2. **مجاني (Free OCR):** استخدام OCR.space
3. **هجين (Hybrid):** يجرب المحلي أولاً، ثم المجاني (افتراضي)

## الملفات الرئيسية

- `lib/main.dart` - نقطة دخول التطبيق
- `lib/screens/home_screen.dart` - الشاشة الرئيسية
- `lib/screens/result_screen.dart` - شاشة عرض النتائج
- `lib/services/mathpix_service.dart` - خدمة Mathpix API
- `lib/config/api_config.dart` - إعدادات API

## المميزات المتاحة

✅ التقاط صورة من الكاميرا  
✅ اختيار صورة من المعرض  
✅ تحويل الصورة إلى LaTeX  
✅ معاينة المعادلة  
✅ نسخ الكود  
✅ مشاركة الكود  
✅ واجهة عربية  
✅ تصميم متجاوب  

## استكشاف الأخطاء

### مشكلة "مفاتيح API غير مكونة"
- تأكد من إدخال مفاتيح Mathpix الصحيحة في `api_config.dart`

### مشكلة "خطأ في الاتصال"
- تحقق من اتصال الإنترنت
- تأكد من صحة مفاتيح API

### مشكلة أذونات الكاميرا/المعرض
- تأكد من منح التطبيق الأذونات المطلوبة
- على Android: اذهب إلى الإعدادات > التطبيقات > [اسم التطبيق] > الأذونات

## ملاحظات مهمة

- يتطلب التطبيق اتصال بالإنترنت للعمل
- الخطة المجانية من Mathpix تسمح بـ 1000 طلب شهرياً
- للحصول على أفضل النتائج، استخدم صور واضحة ومقروءة

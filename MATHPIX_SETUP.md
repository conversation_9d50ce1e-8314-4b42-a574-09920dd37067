# دليل إعداد Mathpix API

## خطوات الحصول على مفاتيح API

### 1. إنشاء حساب Mathpix

1. اذهب إلى [Mathpix Dashboard](https://dashboard.mathpix.com/)
2. انقر على "Sign Up" لإنشاء حساب جديد
3. أدخل بياناتك (الاسم، البريد الإلكتروني، كلمة المرور)
4. تأكد من بريدك الإلكتروني

### 2. الحصول على المفاتيح

1. بعد تسجيل الدخول، اذهب إلى لوحة التحكم
2. انقر على "API Keys" أو "Applications"
3. انقر على "Create New Application"
4. أدخل اسم التطبيق (مثل: "Flutter LaTeX Converter")
5. اختر نوع التطبيق: "Mobile App"
6. احفظ التطبيق

### 3. نسخ المفاتيح

بعد إنشاء التطبيق، ستحصل على:
- **App ID**: معرف التطبيق
- **App Key**: مفتاح التطبيق

### 4. إدخال المفاتيح في التطبيق

1. افتح ملف `lib/services/mathpix_service.dart`
2. ابحث عن هذه الأسطر:
   ```dart
   static const String _appId = 'YOUR_APP_ID';
   static const String _appKey = 'YOUR_APP_KEY';
   ```
3. استبدل `YOUR_APP_ID` بـ App ID الخاص بك
4. استبدل `YOUR_APP_KEY` بـ App Key الخاص بك

### مثال:
```dart
static const String _appId = 'your_actual_app_id_here';
static const String _appKey = 'your_actual_app_key_here';
```

## الخطة المجانية

Mathpix يوفر خطة مجانية تتضمن:
- 1000 طلب شهرياً
- دعم للمعادلات الرياضية والكيميائية
- جودة عالية في التحويل

## نصائح مهمة

1. **احتفظ بالمفاتيح آمنة**: لا تشارك مفاتيح API مع أحد
2. **لا تضع المفاتيح في Git**: تأكد من عدم رفع المفاتيح إلى المستودع العام
3. **راقب الاستخدام**: تحقق من استهلاكك الشهري في لوحة التحكم
4. **اختبر الاتصال**: تأكد من عمل المفاتيح قبل نشر التطبيق

## استكشاف الأخطاء

### خطأ "Invalid API Key"
- تأكد من صحة App ID و App Key
- تأكد من عدم وجود مسافات إضافية
- تأكد من أن التطبيق مفعل في لوحة التحكم

### خطأ "Quota Exceeded"
- تحقق من استهلاكك الشهري
- انتظر حتى بداية الشهر التالي أو ترقية الخطة

### خطأ في الاتصال
- تأكد من اتصال الإنترنت
- تحقق من إعدادات الشبكة والجدار الناري

## روابط مفيدة

- [Mathpix Dashboard](https://dashboard.mathpix.com/)
- [Mathpix API Documentation](https://docs.mathpix.com/)
- [أسعار Mathpix](https://mathpix.com/pricing)
- [دعم Mathpix](https://mathpix.com/support)

# محول الصور إلى LaTeX 🚀

تطبيق Flutter مجاني لتحويل الصور التي تحتوي على معادلات رياضية إلى كود LaTeX باستخدام خدمات OCR مجانية ومدمجة.

## ✨ المميزات

- 📷 **التقاط صورة من الكاميرا**
- 🖼️ **اختيار صورة من المعرض**
- 🔄 **تحويل تلقائي إلى LaTeX** باستخدام خدمات مجانية
- 👁️ **معاينة بصرية للمعادلة**
- 📋 **نسخ الكود إلى الحافظة**
- 📤 **مشاركة الكود**
- 🌐 **واجهة عربية كاملة**
- 🆓 **مجاني بالكامل - لا يتطلب مفاتيح API مدفوعة**

## 🛠️ الخدمات المدمجة

يستخدم التطبيق مجموعة من الخدمات المجانية:

1. **Google ML Kit** - للتعرف على النص محلياً
2. **OCR.space API** - خدمة OCR مجانية عبر الإنترنت  
3. **محرك تحويل ذكي** - لتحويل النص إلى LaTeX

## 📋 متطلبات النظام

- Flutter SDK 3.8.1 أو أحدث
- Dart 3.0+
- Android 5.0+ أو iOS 11.0+
- اتصال بالإنترنت (للخدمة المجانية)

## 🚀 التثبيت والإعداد

### 1. استنساخ المشروع

```bash
git clone [repository-url]
cd latex_converter
```

### 2. تثبيت التبعيات

```bash
flutter pub get
```

### 3. تشغيل التطبيق

```bash
flutter run
```

**🎉 لا حاجة لإعداد مفاتيح API! التطبيق جاهز للاستخدام فوراً.**

## 📦 التبعيات المستخدمة

```yaml
dependencies:
  flutter: sdk: flutter
  cupertino_icons: ^1.0.8
  http: ^1.1.0                    # طلبات HTTP
  image_picker: ^1.0.4           # اختيار الصور
  flutter_math_fork: ^0.7.2      # عرض LaTeX
  share_plus: ^7.2.1             # مشاركة المحتوى
  path_provider: ^2.1.1          # إدارة الملفات
  google_ml_kit: ^0.16.3         # التعرف على النص
  image: ^4.1.3                  # معالجة الصور
```

## 🎯 كيفية الاستخدام

1. **افتح التطبيق**
2. **اختر مصدر الصورة:**
   - 📷 التقاط صورة جديدة
   - 🖼️ اختيار من المعرض
3. **انتظر التحويل التلقائي**
4. **اعرض النتيجة:**
   - كود LaTeX
   - معاينة المعادلة
5. **انسخ أو شارك الكود**

## 🔧 الاختبار

```bash
# تشغيل الاختبارات
flutter test

# فحص جودة الكود
flutter analyze
```

## 📁 هيكل المشروع

```
lib/
├── main.dart                    # نقطة دخول التطبيق
├── config/
│   └── api_config.dart         # إعدادات الخدمات
├── screens/
│   ├── home_screen.dart        # الشاشة الرئيسية
│   └── result_screen.dart      # شاشة النتائج
└── services/
    ├── mathpix_service.dart    # الخدمة الموحدة
    ├── local_ocr_service.dart  # Google ML Kit
    └── free_ocr_service.dart   # OCR.space
```

## 🌟 المميزات التقنية

- **تحويل هجين:** يجمع بين الخدمات المحلية والسحابية
- **معالجة ذكية:** تحويل متقدم للرموز الرياضية
- **واجهة متجاوبة:** تدعم أحجام الشاشات المختلفة
- **معالجة أخطاء شاملة:** رسائل واضحة بالعربية
- **أداء محسن:** تحميل سريع ومعالجة فعالة

## 🔍 أمثلة على التحويل

| النوع | المدخل | المخرج |
|-------|---------|---------|
| كسر | `1/2` | `\frac{1}{2}` |
| أس | `x^2` | `x^{2}` |
| جذر | `√x` | `\sqrt{x}` |
| رموز | `α, β, π` | `\alpha, \beta, \pi` |

## 🐛 استكشاف الأخطاء

### مشكلة "لم يتم العثور على نص"
- تأكد من وضوح الصورة
- استخدم إضاءة جيدة
- تأكد من أن النص مقروء

### مشكلة "خطأ في الاتصال"
- تحقق من اتصال الإنترنت
- أعد المحاولة بعد قليل

### مشكلة أذونات الكاميرا
- اذهب إلى إعدادات التطبيق
- امنح أذونات الكاميرا والمعرض

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 🤝 المساهمة

نرحب بالمساهمات! يرجى:

1. عمل Fork للمشروع
2. إنشاء فرع للميزة الجديدة
3. إجراء التغييرات
4. إرسال Pull Request

## 📞 الدعم

إذا واجهت أي مشاكل أو لديك اقتراحات، يرجى فتح [Issue](../../issues) جديد.

---

**صنع بـ ❤️ باستخدام Flutter**

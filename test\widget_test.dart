import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:latex/main.dart';

void main() {
  testWidgets('تطبيق محول الصور إلى LaTeX يعمل بشكل صحيح', (
    WidgetTester tester,
  ) async {
    // بناء التطبيق وتشغيل إطار
    await tester.pumpWidget(const MyApp());

    // التحقق من وجود العنوان الرئيسي
    expect(find.text('محول الصور إلى LaTeX'), findsOneWidget);

    // التحقق من وجود أزرار الاختيار
    expect(find.text('التقاط صورة 📷'), findsOneWidget);
    expect(find.text('اختيار من المعرض 🖼️'), findsOneWidget);

    // التحقق من وجود الوصف
    expect(find.text('مرحباً بك في محول الصور إلى LaTeX'), findsOneWidget);
  });

  testWidgets('أزرار التطبيق موجودة', (WidgetTester tester) async {
    await tester.pumpWidget(const MyApp());

    // البحث عن أزرار الكاميرا والمعرض
    final cameraButton = find.text('التقاط صورة 📷');
    final galleryButton = find.text('اختيار من المعرض 🖼️');

    // التحقق من وجود الأزرار
    expect(cameraButton, findsOneWidget);
    expect(galleryButton, findsOneWidget);

    // التحقق من وجود أيقونات الأزرار
    expect(find.byIcon(Icons.camera_alt), findsOneWidget);
    expect(find.byIcon(Icons.photo_library), findsOneWidget);
  });
}

/// ملف تكوين API
/// يحتوي على إعدادات Mathpix API
class ApiConfig {
  // مفاتيح Mathpix API
  // يرجى استبدال هذه القيم بمفاتيحك الحقيقية من Mathpix Dashboard
  static const String mathpixAppId = 'YOUR_APP_ID';
  static const String mathpixAppKey = 'YOUR_APP_KEY';
  
  // رابط API الأساسي
  static const String mathpixBaseUrl = 'https://api.mathpix.com/v3/text';
  
  // إعدادات الطلب
  static const int requestTimeoutSeconds = 30;
  static const int maxImageSizeKB = 1024; // 1 MB
  
  // التحقق من صحة المفاتيح
  static bool get areKeysConfigured {
    return mathpixAppId != 'YOUR_APP_ID' && 
           mathpixAppKey != 'YOUR_APP_KEY' &&
           mathpixAppId.isNotEmpty && 
           mathpixAppKey.isNotEmpty;
  }
  
  // رسالة خطأ للمفاتيح غير المكونة
  static const String keysNotConfiguredMessage = 
      'يرجى إدخال مفاتيح Mathpix API في ملف lib/config/api_config.dart\n'
      'راجع ملف MATHPIX_SETUP.md للحصول على التعليمات';
}

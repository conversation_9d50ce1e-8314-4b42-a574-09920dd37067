/// ملف تكوين خدمات OCR
/// يحتوي على إعدادات الخدمات المجانية المدمجة
class ApiConfig {
  // إعدادات خدمة OCR.space المجانية
  static const String ocrSpaceApiKey = 'K87899142388957'; // مفتاح مجاني
  static const String ocrSpaceBaseUrl = 'https://api.ocr.space/parse/image';

  // إعدادات الطلب
  static const int requestTimeoutSeconds = 30;
  static const int maxImageSizeKB = 1024; // 1 MB

  // معلومات الخدمات المتاحة
  static const List<String> availableServices = [
    'Google ML Kit (محلي)',
    'OCR.space (مجاني)',
    'تحويل ذكي إلى LaTeX',
  ];

  // رسائل النظام
  static const String successMessage = 'التطبيق يستخدم خدمات OCR مجانية ومدمجة';

  static const String networkErrorMessage =
      'تحقق من اتصال الإنترنت وحاول مرة أخرى';

  // التحقق من توفر الخدمات
  static bool get areServicesAvailable {
    return true; // الخدمات المجانية متاحة دائماً
  }

  // معلومات حول الخدمات
  static String get servicesInfo {
    return 'يستخدم التطبيق خدمات OCR مجانية مع تحويل ذكي إلى LaTeX';
  }
}
